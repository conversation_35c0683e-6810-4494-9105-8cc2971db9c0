import { generateColorPalette } from './colorPalette.js';

/**
 * Get the current website's color palettes
 * This function can be used in components to access the generated color palettes
 * @param {Object} locals - Astro.locals object
 * @returns {Object} Object containing primary and secondary color palettes
 */
export function getWebsiteColors(locals) {
  const { websiteTheme } = locals || {};
  
  if (!websiteTheme) {
    return {
      primary: generateColorPalette('#3B82F6', 'primary'),
      secondary: generateColorPalette('#10B981', 'secondary')
    };
  }

  return {
    primary: generateColorPalette(websiteTheme.primary_color || '#3B82F6', 'primary'),
    secondary: generateColorPalette(websiteTheme.secondary_color || '#10B981', 'secondary')
  };
}

/**
 * Get a specific color from the website's palette
 * @param {Object} locals - Astro.locals object
 * @param {'primary' | 'secondary'} palette - Which palette to use
 * @param {string | number} shade - The shade (50, 100, 200, etc.)
 * @returns {string} The hex color value
 */
export function getWebsiteColor(locals, palette = 'primary', shade = 500) {
  const colors = getWebsiteColors(locals);
  return colors[palette]?.[shade] || colors.primary[500];
}

/**
 * Generate CSS custom properties for inline styles
 * @param {Object} locals - Astro.locals object
 * @returns {string} CSS custom properties string for inline styles
 */
export function getWebsiteColorsCSSProps(locals) {
  const colors = getWebsiteColors(locals);
  let cssProps = '';
  
  // Add primary colors
  for (const [shade, color] of Object.entries(colors.primary)) {
    cssProps += `--primary-${shade}: ${color}; `;
  }
  
  // Add secondary colors
  for (const [shade, color] of Object.entries(colors.secondary)) {
    cssProps += `--secondary-${shade}: ${color}; `;
  }
  
  return cssProps.trim();
}
