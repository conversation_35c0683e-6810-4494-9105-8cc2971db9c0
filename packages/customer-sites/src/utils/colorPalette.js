import Color from 'colorjs.io';

/**
 * Generates a Tailwind-compatible color palette from a base color
 * @param {string} baseColor - The base color in any CSS-compatible format (hex, rgb, hsl, etc.)
 * @param {string} paletteName - The name of the palette (e.g., 'primary', 'secondary')
 * @returns {Object} An object with color shades from 50 to 950
 */
export function generateColorPalette(baseColor, paletteName = 'primary') {
  try {
    const color = new Color(baseColor);
    
    // Convert to LCH for better perceptual uniformity
    const lch = color.to('lch');
    const baseLightness = lch.l;
    const baseChroma = lch.c;
    const baseHue = lch.h;

    // Define lightness values for each shade (based on Tailwind's default palettes)
    const lightnessMap = {
      50: Math.min(95, baseLightness + (95 - baseLightness) * 0.9),
      100: Math.min(90, baseLightness + (90 - baseLightness) * 0.8),
      200: Math.min(85, baseLightness + (85 - baseLightness) * 0.6),
      300: Math.min(75, baseLightness + (75 - baseLightness) * 0.4),
      400: Math.min(65, baseLightness + (65 - baseLightness) * 0.2),
      500: baseLightness, // Base color
      600: Math.max(35, baseLightness - (baseLightness - 35) * 0.2),
      700: Math.max(25, baseLightness - (baseLightness - 25) * 0.4),
      800: Math.max(15, baseLightness - (baseLightness - 15) * 0.6),
      900: Math.max(10, baseLightness - (baseLightness - 10) * 0.8),
      950: Math.max(5, baseLightness - (baseLightness - 5) * 0.9)
    };

    // Generate the palette
    const palette = {};
    
    for (const [shade, lightness] of Object.entries(lightnessMap)) {
      // Adjust chroma for very light and very dark colors
      let adjustedChroma = baseChroma;
      if (lightness > 85) {
        // Reduce chroma for very light colors
        adjustedChroma = baseChroma * (0.3 + (95 - lightness) / 10 * 0.7);
      } else if (lightness < 20) {
        // Reduce chroma for very dark colors
        adjustedChroma = baseChroma * (0.4 + lightness / 20 * 0.6);
      }

      // Create the color in LCH space
      const shadeColor = new Color('lch', [lightness, adjustedChroma, baseHue]);
      
      // Convert to sRGB and clamp to ensure valid colors
      const srgb = shadeColor.to('srgb').toGamut();
      
      // Convert to hex
      palette[shade] = srgb.toString({ format: 'hex' });
    }

    return palette;
  } catch (error) {
    console.error(`Error generating color palette for ${paletteName}:`, error);
    
    // Return a fallback palette based on the palette name
    const fallbackColors = {
      primary: {
        50: '#eff6ff',
        100: '#dbeafe',
        200: '#bfdbfe',
        300: '#93c5fd',
        400: '#60a5fa',
        500: '#3b82f6',
        600: '#2563eb',
        700: '#1d4ed8',
        800: '#1e40af',
        900: '#1e3a8a',
        950: '#172554'
      },
      secondary: {
        50: '#ecfdf5',
        100: '#d1fae5',
        200: '#a7f3d0',
        300: '#6ee7b7',
        400: '#34d399',
        500: '#10b981',
        600: '#059669',
        700: '#047857',
        800: '#065f46',
        900: '#064e3b',
        950: '#022c22'
      }
    };

    return fallbackColors[paletteName] || fallbackColors.primary;
  }
}

/**
 * Generates CSS custom properties for the color palettes
 * @param {string} primaryColor - The primary color
 * @param {string} secondaryColor - The secondary color
 * @returns {string} CSS custom properties string
 */
export function generateColorPaletteCSSVars(primaryColor, secondaryColor) {
  const primaryPalette = generateColorPalette(primaryColor, 'primary');
  const secondaryPalette = generateColorPalette(secondaryColor, 'secondary');

  let cssVars = '';

  // Generate primary color variables
  for (const [shade, color] of Object.entries(primaryPalette)) {
    cssVars += `  --color-primary-${shade}: ${color};\n`;
  }

  // Generate secondary color variables
  for (const [shade, color] of Object.entries(secondaryPalette)) {
    cssVars += `  --color-secondary-${shade}: ${color};\n`;
  }

  return cssVars;
}

/**
 * Generates Tailwind CSS @theme configuration for the color palettes
 * @param {string} primaryColor - The primary color
 * @param {string} secondaryColor - The secondary color
 * @returns {string} Tailwind @theme configuration string
 */
export function generateTailwindThemeConfig(primaryColor, secondaryColor) {
  const primaryPalette = generateColorPalette(primaryColor, 'primary');
  const secondaryPalette = generateColorPalette(secondaryColor, 'secondary');

  let themeConfig = '';

  // Generate primary color theme variables
  for (const [shade, color] of Object.entries(primaryPalette)) {
    themeConfig += `  --color-primary-${shade}: ${color};\n`;
  }

  // Generate secondary color theme variables
  for (const [shade, color] of Object.entries(secondaryPalette)) {
    themeConfig += `  --color-secondary-${shade}: ${color};\n`;
  }

  return themeConfig;
}
