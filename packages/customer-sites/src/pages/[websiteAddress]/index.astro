---
import BaseLayout from '../../layouts/BaseLayout.astro';
// Component Imports
import Hero from '../../components/Hero.astro';
import FeaturedProperties from '../../components/FeaturedProperties.astro';
import { PopularNeighborhoods } from '../../components/PopularNeighborhoods';
import AboutUsPreview from '../../components/AboutUsPreview.astro';
import NotFoundIllustration from "@platform/assets/illustrations/erro-404.svg";

export const prerender = false;

// Get the website address from the URL parameters
const { websiteAddress } = Astro.params;

// Access the website data from Astro.locals
const { websiteInfo, websiteTheme, teamProfile, websiteError } = Astro.locals;

// Check if there's a website error and set error messages
let hasError = false;
let title = "";
let message = "";
let explanation = "";

if (websiteError) {
    hasError = true;
    // Determine the identifier and whether it's a domain
    const identifier = websiteError.identifier;
    const isDomain = identifier.includes('.');
    
    if (websiteError.type === 'website-not-found') {
        title = "Site não encontrado";
        message = isDomain
            ? `O domínio "${identifier}" não está registrado no nosso sistema.`
            : `O subdomínio "${identifier}" não está registrado no nosso sistema.`;
        explanation = "Verifique se o endereço foi digitado corretamente ou entre em contato com o administrador.";
    } else if (websiteError.type === 'website-not-published') {
        title = "Site não publicado";
        message = isDomain
            ? `O site associado ao domínio "${identifier}" existe, mas ainda não foi publicado.`
            : `O site associado ao subdomínio "${identifier}" existe, mas ainda não foi publicado.`;
        explanation = "Entre em contato com o administrador do site para solicitar a publicação.";
    }
}
---

<BaseLayout>
    {hasError ? (
        <main class="px-4 py-16 flex flex-col items-center justify-center">
            <div class="text-center flex flex-col items-center justify-center">
                <NotFoundIllustration class="w-128 h-128 -my-12" />
                <h1 class="text-6xl font-bold text-gray-800 mb-4">404</h1>
                <h2 class="text-2xl font-semibold text-gray-700 mb-6">{title}</h2>

                <div class="bg-white p-8 rounded-lg shadow-md">
                    <p class="text-lg text-gray-600 mb-4">{message}</p>
                    <p class="text-gray-500 mb-8">{explanation}</p>

                    <div class="flex flex-col sm:flex-row justify-center gap-4">
                        <a
                            href="https://imoblr.com.br"
                            class="px-6 py-3 bg-blue-600 text-white font-medium rounded-md hover:bg-blue-700 transition-colors"
                        >
                            Ir para Imoblr
                        </a>

                        <a
                            href="https://imoblr.com.br/contato"
                            target="_blank"
                            class="px-6 py-3 bg-gray-200 text-gray-700 font-medium rounded-md hover:bg-gray-300 transition-colors"
                        >
                            Contatar suporte
                        </a>
                    </div>
                </div>
            </div>
        </main>
    ) : (
        <>
            <div class="relative">
                <!-- Hero Component -->
                <Hero />
            </div>

            <!-- Main Content -->
            <main>
                <!-- Featured Properties Section -->
                <FeaturedProperties />

                <!-- About Us Preview Section -->
                <AboutUsPreview />

                <!-- Popular Neighborhoods Section -->
                <PopularNeighborhoods
                    client:load
                    teamId={websiteInfo?.teamId}
                    primaryColor={websiteTheme?.primary_color || '#3B82F6'}
                />
            </main>
        </>
    )}
</BaseLayout>
