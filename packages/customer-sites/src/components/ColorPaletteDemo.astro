---
// Demo component to showcase the dynamic color palettes
import DynamicColorButton, {
  DynamicColorCard,
  DynamicColorBadge,
} from "./DynamicColorButton.jsx";
---

<div class="p-8 space-y-8">
  <h2 class="text-2xl font-bold text-gray-800 mb-6">
    Dynamic Color Palette Demo
  </h2>

  <!-- Primary Color Palette -->
  <div class="space-y-4">
    <h3 class="text-lg font-semibold text-gray-700">Primary Colors</h3>
    <div class="grid grid-cols-11 gap-2">
      <div
        class="bg-primary-50 p-4 rounded text-center text-xs font-medium text-gray-800"
      >
        50
      </div>
      <div
        class="bg-primary-100 p-4 rounded text-center text-xs font-medium text-gray-800"
      >
        100
      </div>
      <div
        class="bg-primary-200 p-4 rounded text-center text-xs font-medium text-gray-800"
      >
        200
      </div>
      <div
        class="bg-primary-300 p-4 rounded text-center text-xs font-medium text-gray-800"
      >
        300
      </div>
      <div
        class="bg-primary-400 p-4 rounded text-center text-xs font-medium text-white"
      >
        400
      </div>
      <div
        class="bg-primary-500 p-4 rounded text-center text-xs font-medium text-white"
      >
        500
      </div>
      <div
        class="bg-primary-600 p-4 rounded text-center text-xs font-medium text-white"
      >
        600
      </div>
      <div
        class="bg-primary-700 p-4 rounded text-center text-xs font-medium text-white"
      >
        700
      </div>
      <div
        class="bg-primary-800 p-4 rounded text-center text-xs font-medium text-white"
      >
        800
      </div>
      <div
        class="bg-primary-900 p-4 rounded text-center text-xs font-medium text-white"
      >
        900
      </div>
      <div
        class="bg-primary-950 p-4 rounded text-center text-xs font-medium text-white"
      >
        950
      </div>
    </div>
  </div>

  <!-- Secondary Color Palette -->
  <div class="space-y-4">
    <h3 class="text-lg font-semibold text-gray-700">Secondary Colors</h3>
    <div class="grid grid-cols-11 gap-2">
      <div
        class="bg-secondary-50 p-4 rounded text-center text-xs font-medium text-gray-800"
      >
        50
      </div>
      <div
        class="bg-secondary-100 p-4 rounded text-center text-xs font-medium text-gray-800"
      >
        100
      </div>
      <div
        class="bg-secondary-200 p-4 rounded text-center text-xs font-medium text-gray-800"
      >
        200
      </div>
      <div
        class="bg-secondary-300 p-4 rounded text-center text-xs font-medium text-gray-800"
      >
        300
      </div>
      <div
        class="bg-secondary-400 p-4 rounded text-center text-xs font-medium text-white"
      >
        400
      </div>
      <div
        class="bg-secondary-500 p-4 rounded text-center text-xs font-medium text-white"
      >
        500
      </div>
      <div
        class="bg-secondary-600 p-4 rounded text-center text-xs font-medium text-white"
      >
        600
      </div>
      <div
        class="bg-secondary-700 p-4 rounded text-center text-xs font-medium text-white"
      >
        700
      </div>
      <div
        class="bg-secondary-800 p-4 rounded text-center text-xs font-medium text-white"
      >
        800
      </div>
      <div
        class="bg-secondary-900 p-4 rounded text-center text-xs font-medium text-white"
      >
        900
      </div>
      <div
        class="bg-secondary-950 p-4 rounded text-center text-xs font-medium text-white"
      >
        950
      </div>
    </div>
  </div>

  <!-- Usage Examples -->
  <div class="space-y-4">
    <h3 class="text-lg font-semibold text-gray-700">Usage Examples</h3>
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
      <!-- Primary Button -->
      <button
        class="bg-primary-500 hover:bg-primary-600 text-white px-6 py-3 rounded-lg font-medium transition-colors"
      >
        Primary Button
      </button>

      <!-- Secondary Button -->
      <button
        class="bg-secondary-500 hover:bg-secondary-600 text-white px-6 py-3 rounded-lg font-medium transition-colors"
      >
        Secondary Button
      </button>

      <!-- Outlined Primary -->
      <button
        class="border-2 border-primary-500 text-primary-500 hover:bg-primary-50 px-6 py-3 rounded-lg font-medium transition-colors"
      >
        Outlined Primary
      </button>

      <!-- Card with Primary Accent -->
      <div
        class="bg-white border-l-4 border-primary-500 p-4 rounded-lg shadow-sm"
      >
        <h4 class="text-primary-700 font-semibold">Primary Card</h4>
        <p class="text-gray-600 text-sm mt-1">
          Card with primary accent border
        </p>
      </div>

      <!-- Badge -->
      <span
        class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-secondary-100 text-secondary-800"
      >
        Secondary Badge
      </span>

      <!-- Alert -->
      <div
        class="bg-primary-50 border border-primary-200 text-primary-800 px-4 py-3 rounded-lg"
      >
        <p class="text-sm">This is a primary alert message</p>
      </div>
    </div>
  </div>

  <!-- React Component Examples -->
  <div class="space-y-4">
    <h3 class="text-lg font-semibold text-gray-700">
      React Component Examples
    </h3>
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
      <!-- Dynamic Buttons -->
      <DynamicColorButton client:load>Primary Button</DynamicColorButton>
      <DynamicColorButton client:load variant="secondary"
        >Secondary Button</DynamicColorButton
      >
      <DynamicColorButton client:load variant="primary-outline"
        >Outlined Primary</DynamicColorButton
      >
      <DynamicColorButton client:load variant="secondary-ghost"
        >Secondary Ghost</DynamicColorButton
      >

      <!-- Dynamic Card -->
      <DynamicColorCard client:load accent="primary">
        <h4 class="text-primary-700 font-semibold mb-2">Dynamic Card</h4>
        <p class="text-gray-600 text-sm">
          This card uses the dynamic primary color accent.
        </p>
      </DynamicColorCard>

      <!-- Dynamic Badges -->
      <div class="space-x-2">
        <DynamicColorBadge client:load color="primary"
          >Primary Badge</DynamicColorBadge
        >
        <DynamicColorBadge client:load color="secondary"
          >Secondary Badge</DynamicColorBadge
        >
      </div>
    </div>
  </div>

  <!-- Code Examples -->
  <div class="space-y-4">
    <h3 class="text-lg font-semibold text-gray-700">Code Examples</h3>
    <div class="bg-gray-100 p-4 rounded-lg">
      <pre
        class="text-sm text-gray-800"><code>&lt;!-- Background colors --&gt;
&lt;div class="bg-primary-100"&gt;Light primary background&lt;/div&gt;
&lt;div class="bg-primary-500"&gt;Primary background&lt;/div&gt;
&lt;div class="bg-primary-900"&gt;Dark primary background&lt;/div&gt;

&lt;!-- Text colors --&gt;
&lt;p class="text-primary-600"&gt;Primary text&lt;/p&gt;
&lt;p class="text-secondary-700"&gt;Secondary text&lt;/p&gt;

&lt;!-- Border colors --&gt;
&lt;div class="border border-primary-300"&gt;Primary border&lt;/div&gt;
&lt;div class="border-l-4 border-secondary-500"&gt;Secondary left border&lt;/div&gt;

&lt;!-- Hover states --&gt;
&lt;button class="bg-primary-500 hover:bg-primary-600"&gt;Hover effect&lt;/button&gt;</code></pre>
    </div>
  </div>
</div>
