---
import PropertySearchTabs from "../components/PropertySearchTabs.jsx";

// Get website info and theme from Astro.locals (set by middleware)
// Add fallback values in case the properties don't exist in Astro.locals
const defaultWebsiteTheme = {
  primary_color: "#3B82F6",
  secondary_color: "#10B981",
  font_family: "Inter",
};

// Get values from Astro.locals
const { websiteInfo, websiteTheme } = Astro.locals || {};

// Use fallbacks if values are not available
const theme = websiteTheme || defaultWebsiteTheme;

// Calculate a darker version of the primary color for headings
import Color from "colorjs.io";

// Create a darker version of primary_color for headings
let headingColor = "#6b0f2b"; // Default fallback color
let lighterHeadingColor = "#6b0f2b"; // Default fallback color
if (theme?.primary_color) {
  try {
    // Create a Color object from the primary color
    const primaryColor = new Color(theme.primary_color);

    // Mix with black to darken it (30% black, 70% primary color)
    const darkColor = Color.mix(primaryColor, "black", 0.3, { space: "lch" });

    // Convert to sRGB and get the hex string
    headingColor = darkColor.to("srgb").toString({ format: "hex" });
    lighterHeadingColor = Color.mix(primaryColor, "white", 0.9, {
      space: "lch",
    })
      .to("srgb")
      .toString({ format: "hex" });
  } catch (error) {
    console.error("Error calculating heading color:", error);
    // Keep the default fallback color if there's an error
  }
}

// No need to fetch website info here as it's already available in Astro.locals

// Get the hero image URL if available from the website object
const heroImageUrl = websiteInfo?.website?.hero_image_url;

// Defining the hero image source
// Use the website's hero image if available, otherwise use a default image
const baseImageUrl =
  heroImageUrl ||
  "https://images.unsplash.com/photo-1512699355324-f07e3106dae5";

// If it's our own image, we don't need to use wsrv.nl optimization
const heroImage = {
  src: heroImageUrl
    ? baseImageUrl
    : `https://wsrv.nl/?url=${baseImageUrl}&w=1920&output=webp&q=85`,
  width: 1920,
  height: 1080,
};

// Log the hero image URL for debugging
console.log("Hero component: heroImageUrl:", heroImageUrl);
console.log("Hero component: final image src:", heroImage.src);

// Extract website address for the search component
const websiteAddress = websiteInfo?.website?.address || "";
---

<!-- Full-width wrapper -->
<div
  class="relative w-full flex h-[60vh] lg:mb-16 bg-cover bg-center items-center justify-center"
  style={`background-image: url('${heroImage.src}');`}
>
  <div
    class="bg-gradient-to-t from-transparent to-black/80 w-full h-full absolute"
  >
  </div>
  <div class="lg:relative flex justify-end w-full h-full z-20">
    <div class="w-full flex items-center">
      <!-- 40rem is 50% of the 80rem container max-width -->
      <div class="lg:p-12 w-full flex flex-col items-center justify-center">
        <h1
          class="!text-white/90 text-xl md:text-2xl lg:text-3xl xl:text-4xl text-center line-clamp-2 font-bold mb-1 heading-color text-shadow-[0_0_4px_rgba(0,0,0,0.1),0_0_16px_rgba(0,0,0,0.1),0_0_64px_rgba(0,0,0,0.1)] p-12"
        >
          {websiteInfo?.title}
        </h1>
        <!-- <p
          class="text-md md:text-lg line-clamp-4 text-white/90 md:text-gray-600 mb-12"
        >
          {websiteInfo?.description}
        </p> -->

        <!-- Property Search Tabs Component -->
        <PropertySearchTabs
          client:load
          primaryColor={theme.primary_color}
          headingColor={headingColor}
          teamId={websiteInfo?.teamId}
          websiteAddress={websiteAddress}
        />
      </div>
    </div>
  </div>
</div>

<style define:vars={{ headingColor, lighterHeadingColor }}>
  .heading-color {
    color: var(--headingColor);
    border-color: var(--headingColor);
    @media screen and (max-width: 768px) {
      color: var(--lighterHeadingColor);
      border-color: var(--lighterHeadingColor);
    }
  }
</style>
