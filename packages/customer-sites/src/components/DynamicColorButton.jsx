import React from 'react';

/**
 * Example React component that uses the dynamic color palettes
 * This demonstrates how to use the Tailwind classes with the dynamic colors
 */
export default function DynamicColorButton({ 
  children, 
  variant = 'primary', 
  size = 'md',
  className = '',
  ...props 
}) {
  const baseClasses = 'font-medium rounded-lg transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2';
  
  const variantClasses = {
    primary: 'bg-primary-500 hover:bg-primary-600 text-white focus:ring-primary-500',
    secondary: 'bg-secondary-500 hover:bg-secondary-600 text-white focus:ring-secondary-500',
    'primary-outline': 'border-2 border-primary-500 text-primary-500 hover:bg-primary-50 focus:ring-primary-500',
    'secondary-outline': 'border-2 border-secondary-500 text-secondary-500 hover:bg-secondary-50 focus:ring-secondary-500',
    'primary-ghost': 'text-primary-600 hover:bg-primary-100 focus:ring-primary-500',
    'secondary-ghost': 'text-secondary-600 hover:bg-secondary-100 focus:ring-secondary-500'
  };
  
  const sizeClasses = {
    sm: 'px-3 py-1.5 text-sm',
    md: 'px-4 py-2 text-base',
    lg: 'px-6 py-3 text-lg'
  };
  
  const classes = [
    baseClasses,
    variantClasses[variant] || variantClasses.primary,
    sizeClasses[size] || sizeClasses.md,
    className
  ].join(' ');
  
  return (
    <button className={classes} {...props}>
      {children}
    </button>
  );
}

/**
 * Example card component using dynamic colors
 */
export function DynamicColorCard({ 
  children, 
  accent = 'primary',
  className = '',
  ...props 
}) {
  const accentClasses = {
    primary: 'border-l-4 border-primary-500',
    secondary: 'border-l-4 border-secondary-500'
  };
  
  const classes = [
    'bg-white rounded-lg shadow-sm p-6',
    accentClasses[accent] || accentClasses.primary,
    className
  ].join(' ');
  
  return (
    <div className={classes} {...props}>
      {children}
    </div>
  );
}

/**
 * Example badge component using dynamic colors
 */
export function DynamicColorBadge({ 
  children, 
  color = 'primary',
  size = 'md',
  className = '',
  ...props 
}) {
  const colorClasses = {
    primary: 'bg-primary-100 text-primary-800',
    secondary: 'bg-secondary-100 text-secondary-800'
  };
  
  const sizeClasses = {
    sm: 'px-2 py-0.5 text-xs',
    md: 'px-2.5 py-1 text-sm',
    lg: 'px-3 py-1.5 text-base'
  };
  
  const classes = [
    'inline-flex items-center rounded-full font-medium',
    colorClasses[color] || colorClasses.primary,
    sizeClasses[size] || sizeClasses.md,
    className
  ].join(' ');
  
  return (
    <span className={classes} {...props}>
      {children}
    </span>
  );
}
