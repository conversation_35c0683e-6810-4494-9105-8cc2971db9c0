import React from 'react';
import { PropertyLocationSelect } from './PropertyLocationSelect';
import { PropertyTypeSelect } from './PropertyTypeSelect';

/**
 * Demo component showing both DaisyUI implementations
 * 
 * Usage Examples:
 * 
 * 1. DaisyUI Select Component (https://daisyui.com/components/select/):
 *    <PropertyLocationSelect variant="select" teamId="your-team-id" primaryColor="#your-color" />
 *    <PropertyTypeSelect variant="select" teamId="your-team-id" primaryColor="#your-color" />
 * 
 * 2. DaisyUI Dropdown Component (https://daisyui.com/components/dropdown/):
 *    <PropertyLocationSelect variant="dropdown" teamId="your-team-id" primaryColor="#your-color" />
 *    <PropertyTypeSelect variant="dropdown" teamId="your-team-id" primaryColor="#your-color" />
 *    
 *    Or simply omit variant (defaults to dropdown):
 *    <PropertyLocationSelect teamId="your-team-id" primaryColor="#your-color" />
 *    <PropertyTypeSelect teamId="your-team-id" primaryColor="#your-color" />
 */
export function DaisyUISelectDemo({ teamId, primaryColor = "#3b82f6" }) {
  return (
    <div className="space-y-8 p-6">
      <div>
        <h2 className="text-2xl font-bold mb-4">DaisyUI Select Components Demo</h2>
        <p className="text-gray-600 mb-6">
          Both components now support DaisyUI Select and Dropdown implementations.
        </p>
      </div>

      {/* DaisyUI Select Implementation */}
      <div className="space-y-4">
        <h3 className="text-xl font-semibold">DaisyUI Select Implementation</h3>
        <p className="text-sm text-gray-600">
          Using <code className="bg-gray-100 px-2 py-1 rounded">variant="select"</code> - 
          Based on <a href="https://daisyui.com/components/select/" className="text-blue-600 underline" target="_blank" rel="noopener noreferrer">
            DaisyUI Select Component
          </a>
        </p>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium mb-2">Property Location (Select)</label>
            <PropertyLocationSelect 
              variant="select" 
              teamId={teamId} 
              primaryColor={primaryColor} 
            />
          </div>
          
          <div>
            <label className="block text-sm font-medium mb-2">Property Type (Select)</label>
            <PropertyTypeSelect 
              variant="select" 
              teamId={teamId} 
              primaryColor={primaryColor} 
            />
          </div>
        </div>
      </div>

      {/* DaisyUI Dropdown Implementation */}
      <div className="space-y-4">
        <h3 className="text-xl font-semibold">DaisyUI Dropdown Implementation</h3>
        <p className="text-sm text-gray-600">
          Using default variant (dropdown) - 
          Based on <a href="https://daisyui.com/components/dropdown/" className="text-blue-600 underline" target="_blank" rel="noopener noreferrer">
            DaisyUI Dropdown Component
          </a>
        </p>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium mb-2">Property Location (Dropdown)</label>
            <PropertyLocationSelect 
              teamId={teamId} 
              primaryColor={primaryColor} 
            />
          </div>
          
          <div>
            <label className="block text-sm font-medium mb-2">Property Type (Dropdown)</label>
            <PropertyTypeSelect 
              teamId={teamId} 
              primaryColor={primaryColor} 
            />
          </div>
        </div>
      </div>

      {/* Usage Instructions */}
      <div className="bg-gray-50 p-4 rounded-lg">
        <h4 className="font-semibold mb-2">Usage Instructions:</h4>
        <ul className="text-sm space-y-1 text-gray-700">
          <li>• <strong>Select variant:</strong> Traditional HTML select with DaisyUI styling</li>
          <li>• <strong>Dropdown variant:</strong> Custom dropdown with better UX and styling</li>
          <li>• Both variants maintain the same API and functionality</li>
          <li>• The dropdown variant provides better mobile experience</li>
          <li>• Both are fully accessible and keyboard navigable</li>
        </ul>
      </div>
    </div>
  );
}
