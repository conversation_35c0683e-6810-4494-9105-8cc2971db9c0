# Dynamic Color Palettes

This system automatically generates Tailwind-compatible color palettes from the website's primary and secondary colors using colorjs.io. The palettes are dynamically injected into the CSS and can be used with standard Tailwind color classes.

## How it Works

1. **Middleware Processing**: The middleware (`src/middleware.js`) fetches the website's theme colors from the API
2. **Color Generation**: Using colorjs.io, it generates full color palettes (50-950 shades) from the primary and secondary colors
3. **CSS Injection**: The generated color variables are injected into the page via the BaseLayout component
4. **Tailwind Integration**: The colors are available as standard Tailwind classes

## Available Color Classes

### Primary Colors
- `bg-primary-50` through `bg-primary-950` - Background colors
- `text-primary-50` through `text-primary-950` - Text colors  
- `border-primary-50` through `border-primary-950` - Border colors
- All other Tailwind color utilities work (ring, shadow, etc.)

### Secondary Colors
- `bg-secondary-50` through `bg-secondary-950` - Background colors
- `text-secondary-50` through `text-secondary-950` - Text colors
- `border-secondary-50` through `border-secondary-950` - Border colors
- All other Tailwind color utilities work

## Usage Examples

```html
<!-- Buttons -->
<button class="bg-primary-500 hover:bg-primary-600 text-white px-4 py-2 rounded">
  Primary Button
</button>

<button class="bg-secondary-500 hover:bg-secondary-600 text-white px-4 py-2 rounded">
  Secondary Button
</button>

<!-- Cards with accent borders -->
<div class="bg-white border-l-4 border-primary-500 p-4">
  <h3 class="text-primary-700">Card Title</h3>
  <p class="text-gray-600">Card content</p>
</div>

<!-- Badges -->
<span class="bg-secondary-100 text-secondary-800 px-2 py-1 rounded-full text-sm">
  Badge
</span>

<!-- Alerts -->
<div class="bg-primary-50 border border-primary-200 text-primary-800 p-4 rounded">
  Alert message
</div>

<!-- Light backgrounds -->
<div class="bg-primary-100">Light primary background</div>
<div class="bg-secondary-100">Light secondary background</div>
```

## Color Shade Guide

- **50-200**: Very light shades, good for backgrounds and subtle accents
- **300-400**: Light shades, good for hover states and secondary elements
- **500**: Base color (matches the original primary/secondary color)
- **600-700**: Darker shades, good for hover states and emphasis
- **800-950**: Very dark shades, good for text and strong emphasis

## Technical Details

### Color Generation Algorithm

The system uses the LCH color space for perceptually uniform color generation:

1. Converts the base color to LCH (Lightness, Chroma, Hue)
2. Generates shades by adjusting lightness while maintaining hue
3. Adjusts chroma for very light/dark colors to ensure good contrast
4. Converts back to sRGB and clamps to valid color gamut
5. Outputs as hex colors for CSS

### Files Involved

- `src/utils/colorPalette.js` - Color generation utilities
- `src/middleware.js` - Generates palettes and adds to context
- `src/layouts/BaseLayout.astro` - Injects CSS variables
- `src/components/ColorPaletteDemo.astro` - Demo component
- `src/pages/color-demo.astro` - Test page

### Fallback Behavior

If color generation fails, the system falls back to:
- Primary: Blue palette (similar to Tailwind's blue-500)
- Secondary: Green palette (similar to Tailwind's emerald-500)

## Testing

Visit `/color-demo` on any customer site to see the generated color palettes and usage examples.

## Performance

- Color generation happens once per request in middleware
- Generated CSS is minimal (only the needed color variables)
- Uses colorjs.io which is already included in the project
- No runtime JavaScript required for color usage
